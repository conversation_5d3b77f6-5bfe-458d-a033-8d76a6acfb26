'use client';

import { useState, useEffect } from 'react';

interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  screenWidth: number;
  userAgent: string;
}

/**
 * 设备检测Hook
 * 检测当前设备类型和屏幕尺寸
 */
export function useDeviceDetection() {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    screenWidth: 1024,
    userAgent: ''
  });

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const detectDevice = () => {
      const width = window.innerWidth;
      const userAgent = navigator.userAgent;

      // 基于屏幕宽度的检测
      const isMobile = width < 768;
      const isTablet = width >= 768 && width < 1024;
      const isDesktop = width >= 1024;

      // 基于User-Agent的辅助检测
      const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
      const isMobileUA = mobileRegex.test(userAgent);

      setDeviceInfo({
        isMobile: isMobile || isMobileUA,
        isTablet: isTablet && !isMobileUA,
        isDesktop: isDesktop && !isMobileUA,
        screenWidth: width,
        userAgent
      });

      setIsLoading(false);
    };

    // 初始检测
    detectDevice();

    // 监听窗口大小变化
    const handleResize = () => {
      detectDevice();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return { ...deviceInfo, isLoading };
}

/**
 * 简化版本 - 只检测是否为移动端
 */
export function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkIsMobile = () => {
      const width = window.innerWidth;
      const userAgent = navigator.userAgent;
      const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
      
      const isMobileDevice = width < 768 || mobileRegex.test(userAgent);
      setIsMobile(isMobileDevice);
      setIsLoading(false);
    };

    checkIsMobile();

    const handleResize = () => {
      checkIsMobile();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return { isMobile, isLoading };
}

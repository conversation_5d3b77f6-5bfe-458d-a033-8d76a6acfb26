'use client';

import React, { Suspense, lazy } from 'react';
import { useIsMobile } from '@/hooks/useDeviceDetection';

interface DeviceAdaptiveProps {
  mobileComponent: React.ComponentType<any>;
  desktopComponent: React.ComponentType<any>;
  loadingComponent?: React.ComponentType;
  [key: string]: any; // 传递给子组件的其他props
}

/**
 * 设备自适应组件
 * 根据设备类型动态加载不同的组件
 */
export function DeviceAdaptive({
  mobileComponent: MobileComponent,
  desktopComponent: DesktopComponent,
  loadingComponent: LoadingComponent,
  ...props
}: DeviceAdaptiveProps) {
  const { isMobile, isLoading } = useIsMobile();

  // 默认加载组件
  const DefaultLoading = LoadingComponent || (() => (
    <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
    </div>
  ));

  // 设备检测中
  if (isLoading) {
    return <DefaultLoading />;
  }

  // 根据设备类型渲染对应组件
  return isMobile ? <MobileComponent {...props} /> : <DesktopComponent {...props} />;
}

/**
 * 懒加载版本的设备自适应组件
 * 只有在需要时才加载对应的组件，减少初始包大小
 */
interface LazyDeviceAdaptiveProps {
  mobileComponentPath: () => Promise<{ default: React.ComponentType<any> }>;
  desktopComponentPath: () => Promise<{ default: React.ComponentType<any> }>;
  loadingComponent?: React.ComponentType;
  [key: string]: any;
}

export function LazyDeviceAdaptive({
  mobileComponentPath,
  desktopComponentPath,
  loadingComponent: LoadingComponent,
  ...props
}: LazyDeviceAdaptiveProps) {
  const { isMobile, isLoading } = useIsMobile();

  // 懒加载组件
  const LazyMobileComponent = lazy(mobileComponentPath);
  const LazyDesktopComponent = lazy(desktopComponentPath);

  // 默认加载组件
  const DefaultLoading = LoadingComponent || (() => (
    <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
    </div>
  ));

  // 设备检测中
  if (isLoading) {
    return <DefaultLoading />;
  }

  // 根据设备类型懒加载对应组件
  return (
    <Suspense fallback={<DefaultLoading />}>
      {isMobile ? (
        <LazyMobileComponent {...props} />
      ) : (
        <LazyDesktopComponent {...props} />
      )}
    </Suspense>
  );
}

/**
 * 简单的条件渲染组件
 * 适用于简单的显示/隐藏逻辑
 */
interface ConditionalRenderProps {
  mobile?: React.ReactNode;
  desktop?: React.ReactNode;
  tablet?: React.ReactNode;
  children?: React.ReactNode;
}

export function ConditionalRender({ mobile, desktop, tablet, children }: ConditionalRenderProps) {
  const { isMobile, isTablet, isDesktop, isLoading } = useDeviceDetection();

  if (isLoading) {
    return children || null;
  }

  if (isMobile && mobile) return <>{mobile}</>;
  if (isTablet && tablet) return <>{tablet}</>;
  if (isDesktop && desktop) return <>{desktop}</>;

  return children || null;
}
